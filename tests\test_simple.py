#!/usr/bin/env python3
"""
简单测试脚本
"""

import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

print("Prisma OCR 简单测试")
print("=" * 30)

# 测试基本模块导入
try:
    from config.settings import AppSettings
    print("✓ 配置模块导入成功")
except Exception as e:
    print(f"✗ 配置模块导入失败: {e}")

try:
    from utils.file_utils import FileUtils
    print("✓ 文件工具模块导入成功")
except Exception as e:
    print(f"✗ 文件工具模块导入失败: {e}")

try:
    from core.ocr_engine import OCREngine
    print("✓ OCR引擎模块导入成功")
except Exception as e:
    print(f"✗ OCR引擎模块导入失败: {e}")

# 测试文件格式检查
try:
    from utils.file_utils import FileUtils
    test_result = FileUtils.is_supported_input_file("test.pdf")
    print(f"✓ 文件格式检查功能正常: PDF支持={test_result}")
except Exception as e:
    print(f"✗ 文件格式检查失败: {e}")

print("\n测试完成！")
