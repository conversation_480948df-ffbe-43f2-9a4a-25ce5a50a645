"""
Web应用控制器
管理pywebview应用的生命周期
"""

import os
import sys
import logging
import webview
from pathlib import Path
from typing import Optional, Dict, Any

from .api_bridge import APIBridge


class WebController:
    """Web应用控制器"""
    
    def __init__(self, debug: bool = False):
        self.logger = logging.getLogger(__name__)
        self.debug = debug
        self.api_bridge = APIBridge()
        self.window: Optional[webview.Window] = None

        # 设置API桥接的窗口控制器引用
        self.api_bridge.set_window_controller(self)

        # 获取HTML文件路径
        self.html_path = self._get_html_path()

        self.logger.info("Web控制器初始化完成")
    
    def _get_html_path(self) -> str:
        """获取HTML文件路径"""
        # 获取项目根目录
        project_root = Path(__file__).parent.parent
        html_file = project_root / "PrismaUI.html"
        
        if not html_file.exists():
            raise FileNotFoundError(f"HTML文件不存在: {html_file}")
        
        return str(html_file)
    
    def create_window(self) -> webview.Window:
        """创建主窗口"""
        try:
            # 创建窗口 - 隐藏系统标题栏，使用自定义标题栏
            self.window = webview.create_window(
                title='Prisma OCR & Translator',
                url=self.html_path,
                js_api=self.api_bridge,
                width=1200,
                height=1000,
                min_size=(800, 800),
                resizable=True,
                shadow=True,
                on_top=False,
                frameless=True  # 隐藏系统标题栏
            )
            
            self.logger.info("主窗口创建成功")
            return self.window
            
        except Exception as e:
            self.logger.error(f"创建窗口失败: {e}")
            raise
    
    def start_application(self):
        """启动应用程序"""
        try:
            # 创建窗口
            self.create_window()
            
            # 设置窗口事件处理
            self._setup_window_events()
            
            self.logger.info("启动Web应用程序")
            
            # 启动webview
            webview.start(debug=self.debug)
            
        except Exception as e:
            self.logger.error(f"启动应用程序失败: {e}")
            raise
        finally:
            # 清理资源
            self.cleanup()
    
    def _setup_window_events(self):
        """设置窗口事件处理"""
        if not self.window:
            return
        
        # 窗口关闭事件
        def on_window_closed():
            self.logger.info("窗口关闭")
            self.cleanup()
        
        # 窗口加载完成事件
        def on_window_loaded():
            self.logger.info("窗口加载完成")
            # 可以在这里执行初始化JavaScript代码
            
        # 注册事件处理器
        self.window.events.closed += on_window_closed
        self.window.events.loaded += on_window_loaded
    
    def show_file_dialog(self, dialog_type: str = 'open', file_types: tuple = None):
        """
        显示文件对话框
        
        Args:
            dialog_type: 对话框类型 ('open' 或 'save')
            file_types: 文件类型过滤器
            
        Returns:
            选择的文件路径
        """
        try:
            if not file_types:
                file_types = (
                    'PDF文件 (*.pdf)',
                    '图像文件 (*.png;*.jpg;*.jpeg;*.bmp;*.tiff;*.tif)',
                    '所有文件 (*.*)'
                )
            
            if dialog_type == 'open':
                result = self.window.create_file_dialog(
                    webview.OPEN_DIALOG,
                    allow_multiple=False,
                    file_types=file_types
                )
            elif dialog_type == 'save':
                result = self.window.create_file_dialog(
                    webview.SAVE_DIALOG,
                    file_types=file_types
                )
            else:
                raise ValueError(f"不支持的对话框类型: {dialog_type}")
            
            if result and len(result) > 0:
                return result[0]
            else:
                return None
                
        except Exception as e:
            self.logger.error(f"显示文件对话框失败: {e}")
            return None
    
    def show_folder_dialog(self):
        """
        显示文件夹选择对话框
        
        Returns:
            选择的文件夹路径
        """
        try:
            result = self.window.create_file_dialog(webview.FOLDER_DIALOG)
            
            if result and len(result) > 0:
                return result[0]
            else:
                return None
                
        except Exception as e:
            self.logger.error(f"显示文件夹对话框失败: {e}")
            return None
    
    def evaluate_js(self, script: str):
        """
        执行JavaScript代码
        
        Args:
            script: JavaScript代码
            
        Returns:
            执行结果
        """
        try:
            if self.window:
                return self.window.evaluate_js(script)
            else:
                self.logger.warning("窗口未创建，无法执行JavaScript")
                return None
                
        except Exception as e:
            self.logger.error(f"执行JavaScript失败: {e}")
            return None
    
    def set_title(self, title: str):
        """
        设置窗口标题
        
        Args:
            title: 窗口标题
        """
        try:
            if self.window:
                self.window.title = title
            else:
                self.logger.warning("窗口未创建，无法设置标题")
                
        except Exception as e:
            self.logger.error(f"设置窗口标题失败: {e}")
    
    def minimize_window(self):
        """最小化窗口"""
        try:
            if self.window:
                self.window.minimize()
            else:
                self.logger.warning("窗口未创建，无法最小化")
                
        except Exception as e:
            self.logger.error(f"最小化窗口失败: {e}")
    
    def maximize_window(self):
        """最大化窗口"""
        try:
            if self.window:
                self.window.maximize()
            else:
                self.logger.warning("窗口未创建，无法最大化")

        except Exception as e:
            self.logger.error(f"最大化窗口失败: {e}")

    def restore_window(self):
        """还原窗口"""
        try:
            if self.window:
                self.window.restore()
            else:
                self.logger.warning("窗口未创建，无法还原")

        except Exception as e:
            self.logger.error(f"还原窗口失败: {e}")

    def get_window_info(self) -> dict[str, Any]:
        """获取窗口信息"""
        try:
            if self.window:
                # pywebview可能没有直接的方法获取窗口状态
                # 这里返回一个基本的信息结构
                return {
                    'maximized': False,  # 默认假设未最大化
                    'minimized': False,
                    'visible': True
                }
            else:
                return {
                    'maximized': False,
                    'minimized': False,
                    'visible': False
                }
        except Exception as e:
            self.logger.error(f"获取窗口信息失败: {e}")
            return {
                'maximized': False,
                'minimized': False,
                'visible': False
            }
    
    def close_window(self):
        """关闭窗口"""
        try:
            if self.window:
                self.window.destroy()
                self.window = None
            else:
                self.logger.warning("窗口未创建或已关闭")
                
        except Exception as e:
            self.logger.error(f"关闭窗口失败: {e}")
    
    def cleanup(self):
        """清理资源"""
        try:
            # 清理API桥接层
            if self.api_bridge:
                self.api_bridge.cleanup()
            
            # 关闭窗口
            if self.window:
                self.window = None
            
            self.logger.info("Web控制器清理完成")
            
        except Exception as e:
            self.logger.error(f"清理资源失败: {e}")
    
    def get_window_info(self) -> dict:
        """
        获取窗口信息
        
        Returns:
            窗口信息字典
        """
        if not self.window:
            return {}
        
        try:
            return {
                'title': self.window.title,
                'width': self.window.width,
                'height': self.window.height,
                'x': self.window.x,
                'y': self.window.y,
                'minimized': self.window.minimized,
                'maximized': self.window.maximized
            }
        except Exception as e:
            self.logger.error(f"获取窗口信息失败: {e}")
            return {}
