"""
API桥接层
处理JavaScript与Python之间的通信
"""

import json
import logging
import threading
import time
from typing import Dict, Any, Optional, Callable
from pathlib import Path

from config.settings import AppSettings
from config.models_config import ModelsConfig
from core.ocr_engine import OCREngine
from utils.system_monitor import SystemMonitor
from utils.file_utils import FileUtils


class APIBridge:
    """API桥接类，处理前后端通信"""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        
        # 初始化组件
        self.settings = AppSettings()
        self.models_config = ModelsConfig()
        self.system_monitor = SystemMonitor()
        self.ocr_engine = None
        
        # 状态管理
        self.current_file = None
        self.processing_status = {
            'is_processing': False,
            'progress': 0,
            'status_text': '等待开始...',
            'error': None
        }
        
        # 系统资源状态
        self.system_resources = {
            'cpu_percent': 0.0,
            'memory_percent': 0.0,
            'gpu_percent': 0.0
        }
        
        # 回调函数
        self.progress_callback = None
        self.resource_callback = None

        # 窗口控制引用
        self.window_controller = None

        # 初始化系统监控
        self._setup_system_monitor()

        self.logger.info("API桥接层初始化完成")
    
    def _setup_system_monitor(self):
        """设置系统监控"""
        def resource_update_callback(resources: Dict[str, float]):
            self.system_resources.update(resources)
            if self.resource_callback:
                self.resource_callback(resources)
        
        self.system_monitor.resource_callback = resource_update_callback
        self.system_monitor.start_monitoring()
    
    def select_file(self, file_path: str) -> dict[str, Any]:
        """
        选择文件
        
        Args:
            file_path: 文件路径
            
        Returns:
            操作结果
        """
        try:
            if not file_path or not Path(file_path).exists():
                return {
                    'success': False,
                    'error': '文件不存在'
                }
            
            if not FileUtils.is_supported_input_file(file_path):
                return {
                    'success': False,
                    'error': '不支持的文件格式'
                }
            
            self.current_file = file_path
            file_info = {
                'name': Path(file_path).name,
                'size': FileUtils.get_file_size_mb(file_path),
                'type': 'PDF' if FileUtils.is_pdf_file(file_path) else '图像'
            }
            
            self.logger.info(f"选择文件: {file_path}")
            
            return {
                'success': True,
                'file_info': file_info
            }
            
        except Exception as e:
            self.logger.error(f"选择文件失败: {e}")
            return {
                'success': False,
                'error': str(e)
            }

    def select_output_folder(self) -> dict[str, Any]:
        """
        选择输出文件夹

        Returns:
            操作结果
        """
        try:
            if not self.window_controller:
                return {
                    'success': False,
                    'error': '窗口控制器未初始化'
                }

            folder_path = self.window_controller.show_folder_dialog()

            if folder_path:
                self.logger.info(f"选择输出文件夹: {folder_path}")
                return {
                    'success': True,
                    'folder_path': folder_path
                }
            else:
                return {
                    'success': False,
                    'error': '用户取消选择'
                }

        except Exception as e:
            self.logger.error(f"选择输出文件夹失败: {e}")
            return {
                'success': False,
                'error': str(e)
            }

    def start_processing(self, config: dict[str, Any]) -> dict[str, Any]:
        """
        开始处理
        
        Args:
            config: 处理配置
            
        Returns:
            操作结果
        """
        try:
            if not self.current_file:
                return {
                    'success': False,
                    'error': '请先选择文件'
                }
            
            if self.processing_status['is_processing']:
                return {
                    'success': False,
                    'error': '正在处理中，请等待完成'
                }
            
            # 启动处理线程
            processing_thread = threading.Thread(
                target=self._process_file,
                args=(self.current_file, config),
                daemon=True
            )
            processing_thread.start()
            
            return {
                'success': True,
                'message': '开始处理'
            }
            
        except Exception as e:
            self.logger.error(f"开始处理失败: {e}")
            return {
                'success': False,
                'error': str(e)
            }
    
    def _process_file(self, file_path: str, config: dict[str, Any]):
        """
        处理文件（在后台线程中运行）
        
        Args:
            file_path: 文件路径
            config: 处理配置
        """
        try:
            self.processing_status.update({
                'is_processing': True,
                'progress': 0,
                'status_text': '正在初始化...',
                'error': None
            })
            
            # 初始化OCR引擎
            if not self.ocr_engine:
                self.ocr_engine = OCREngine(self.settings.ocr, self.models_config)
                
                def progress_update(progress: int, status: str):
                    self.processing_status.update({
                        'progress': progress,
                        'status_text': status
                    })

                # 设置回调函数而不是信号连接
                self.ocr_engine.progress_callback = progress_update
                
                if not self.ocr_engine.initialize():
                    raise RuntimeError("OCR引擎初始化失败")
            
            # 设置处理模式
            mode = config.get('mode', 'general')
            self.ocr_engine.set_mode(mode)
            
            # 模拟处理过程（实际实现中会调用OCR引擎）
            steps = [
                (20, "正在预处理文件..."),
                (40, "正在识别文本..."),
                (60, "正在分析版面..."),
                (80, "正在生成输出..."),
                (100, "处理完成")
            ]
            
            for progress, status in steps:
                if not self.processing_status['is_processing']:
                    break
                
                self.processing_status.update({
                    'progress': progress,
                    'status_text': status
                })
                
                time.sleep(1)  # 模拟处理时间
            
            if self.processing_status['is_processing']:
                self.processing_status.update({
                    'is_processing': False,
                    'progress': 100,
                    'status_text': '处理完成',
                    'error': None
                })
            
        except Exception as e:
            self.logger.error(f"文件处理失败: {e}")
            self.processing_status.update({
                'is_processing': False,
                'progress': 0,
                'status_text': '处理失败',
                'error': str(e)
            })
    
    def stop_processing(self) -> dict[str, Any]:
        """
        停止处理
        
        Returns:
            操作结果
        """
        try:
            if self.processing_status['is_processing']:
                self.processing_status.update({
                    'is_processing': False,
                    'progress': 0,
                    'status_text': '处理已停止',
                    'error': None
                })
                
                self.logger.info("停止处理")
                
                return {
                    'success': True,
                    'message': '已停止处理'
                }
            else:
                return {
                    'success': False,
                    'error': '当前没有正在处理的任务'
                }
                
        except Exception as e:
            self.logger.error(f"停止处理失败: {e}")
            return {
                'success': False,
                'error': str(e)
            }
    
    def get_processing_status(self) -> dict[str, Any]:
        """
        获取处理状态
        
        Returns:
            处理状态
        """
        return self.processing_status.copy()
    
    def get_system_resources(self) -> dict[str, Any]:
        """
        获取系统资源使用情况
        
        Returns:
            系统资源信息
        """
        return self.system_resources.copy()
    
    def get_settings(self) -> dict[str, Any]:
        """
        获取应用设置
        
        Returns:
            应用设置
        """
        return {
            'ocr': {
                'use_gpu': self.settings.ocr.use_gpu,
                'use_angle_cls': self.settings.ocr.use_angle_cls,
                'lang': self.settings.ocr.lang
            },
            'translation': {
                'enabled': self.settings.translation.enabled,
                'target_language': self.settings.translation.target_language
            },
            'output': {
                'default_format': self.settings.output.default_format,
                'output_directory': self.settings.output.output_directory
            }
        }
    
    def update_settings(self, settings_data: dict[str, Any]) -> dict[str, Any]:
        """
        更新应用设置
        
        Args:
            settings_data: 设置数据
            
        Returns:
            操作结果
        """
        try:
            # 更新OCR设置
            if 'ocr' in settings_data:
                ocr_settings = settings_data['ocr']
                if 'use_gpu' in ocr_settings:
                    self.settings.ocr.use_gpu = ocr_settings['use_gpu']
                if 'use_angle_cls' in ocr_settings:
                    self.settings.ocr.use_angle_cls = ocr_settings['use_angle_cls']
                if 'lang' in ocr_settings:
                    self.settings.ocr.lang = ocr_settings['lang']
            
            # 更新翻译设置
            if 'translation' in settings_data:
                trans_settings = settings_data['translation']
                if 'enabled' in trans_settings:
                    self.settings.translation.enabled = trans_settings['enabled']
                if 'target_language' in trans_settings:
                    self.settings.translation.target_language = trans_settings['target_language']
            
            # 更新输出设置
            if 'output' in settings_data:
                output_settings = settings_data['output']
                if 'default_format' in output_settings:
                    self.settings.output.default_format = output_settings['default_format']
                if 'output_directory' in output_settings:
                    self.settings.output.output_directory = output_settings['output_directory']
            
            # 保存设置
            self.settings.save()
            
            self.logger.info("设置更新成功")
            
            return {
                'success': True,
                'message': '设置已更新'
            }
            
        except Exception as e:
            self.logger.error(f"更新设置失败: {e}")
            return {
                'success': False,
                'error': str(e)
            }

    def minimize_window(self) -> dict[str, Any]:
        """最小化窗口"""
        try:
            if self.window_controller and self.window_controller.window:
                self.window_controller.minimize_window()
                return {
                    'success': True,
                    'message': '窗口已最小化'
                }
            else:
                return {
                    'success': False,
                    'error': '窗口控制器未初始化'
                }
        except Exception as e:
            self.logger.error(f"最小化窗口失败: {e}")
            return {
                'success': False,
                'error': str(e)
            }

    def maximize_window(self) -> dict[str, Any]:
        """最大化/还原窗口"""
        try:
            if self.window_controller and self.window_controller.window:
                # 检查当前窗口状态并切换
                window_info = self.window_controller.get_window_info()
                if window_info.get('maximized', False):
                    # 如果已最大化，则还原
                    self.window_controller.restore_window()
                else:
                    # 如果未最大化，则最大化
                    self.window_controller.maximize_window()

                return {
                    'success': True,
                    'message': '窗口状态已切换'
                }
            else:
                return {
                    'success': False,
                    'error': '窗口控制器未初始化'
                }
        except Exception as e:
            self.logger.error(f"切换窗口状态失败: {e}")
            return {
                'success': False,
                'error': str(e)
            }

    def close_window(self) -> dict[str, Any]:
        """关闭窗口"""
        try:
            if self.window_controller and self.window_controller.window:
                self.window_controller.close_window()
                return {
                    'success': True,
                    'message': '窗口已关闭'
                }
            else:
                return {
                    'success': False,
                    'error': '窗口控制器未初始化'
                }
        except Exception as e:
            self.logger.error(f"关闭窗口失败: {e}")
            return {
                'success': False,
                'error': str(e)
            }

    def set_window_controller(self, controller):
        """设置窗口控制器引用"""
        self.window_controller = controller

    def cleanup(self):
        """清理资源"""
        try:
            if self.system_monitor:
                self.system_monitor.stop_monitoring()

            self.logger.info("API桥接层清理完成")

        except Exception as e:
            self.logger.error(f"清理资源失败: {e}")
