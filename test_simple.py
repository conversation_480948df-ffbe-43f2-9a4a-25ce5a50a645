#!/usr/bin/env python3
"""
简单的WebView窗口测试
"""

import webview
import os
from pathlib import Path


def create_simple_api():
    """创建简单的API类用于测试"""
    class SimpleAPI:
        def minimize_window(self):
            return {'success': True, 'message': '最小化窗口'}
        
        def maximize_window(self):
            return {'success': True, 'message': '最大化窗口'}
        
        def close_window(self):
            webview.windows[0].destroy()
            return {'success': True, 'message': '关闭窗口'}
    
    return SimpleAPI()


def main():
    """启动简单的WebView测试"""
    print("启动简单的WebView测试...")
    
    # 获取HTML文件路径
    html_path = Path(__file__).parent / 'PrismaUI.html'
    
    if not html_path.exists():
        print(f"错误: HTML文件不存在: {html_path}")
        return
    
    print(f"使用HTML文件: {html_path}")
    
    # 创建API
    api = create_simple_api()
    
    try:
        # 创建无边框窗口
        window = webview.create_window(
            title='Prisma OCR Test',
            url=str(html_path),
            js_api=api,
            width=1200,
            height=800,
            min_size=(800, 600),
            resizable=True,
            shadow=True,
            frameless=True  # 无边框
        )
        
        print("窗口创建成功，启动WebView...")
        
        # 启动WebView
        webview.start(debug=False)
        
    except Exception as e:
        print(f"启动失败: {e}")


if __name__ == "__main__":
    main()
