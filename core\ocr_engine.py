"""
OCR核心引擎
"""

import os
import logging
import numpy as np
from typing import List, Dict, Any, Optional, Callable, Union
from pathlib import Path
from PIL import Image
# 移除PyQt6依赖，使用回调函数机制, QThread

try:
    import paddleocr
    from paddleocr import PaddleOCR
    PADDLEOCR_AVAILABLE = True
except ImportError:
    PADDLEOCR_AVAILABLE = False

from config.settings import OCRSettings
from config.models_config import ModelsConfig
from utils.image_utils import ImageUtils


class OCRResult:
    """OCR识别结果"""
    
    def __init__(self):
        self.text_blocks: List[Dict[str, Any]] = []
        self.tables: List[Dict[str, Any]] = []
        self.layout_info: Dict[str, Any] = {}
        self.confidence_scores: List[float] = []
        self.processing_time: float = 0.0
        self.page_number: int = 0
    
    def add_text_block(self, text: str, bbox: List[float], confidence: float):
        """添加文本块"""
        self.text_blocks.append({
            'text': text,
            'bbox': bbox,
            'confidence': confidence
        })
        self.confidence_scores.append(confidence)
    
    def add_table(self, table_data: Dict[str, Any]):
        """添加表格数据"""
        self.tables.append(table_data)
    
    def get_all_text(self) -> str:
        """获取所有识别的文本"""
        return '\n'.join([block['text'] for block in self.text_blocks])
    
    def get_average_confidence(self) -> float:
        """获取平均置信度"""
        if not self.confidence_scores:
            return 0.0
        return sum(self.confidence_scores) / len(self.confidence_scores)


class OCREngine:
    """OCR引擎"""
    
    def __init__(self, settings: OCRSettings, models_config: ModelsConfig):
        self.settings = settings
        self.models_config = models_config
        self.logger = logging.getLogger(__name__)

        # OCR实例
        self.ocr_general = None
        self.ocr_table = None
        self.ocr_layout = None

        # 初始化状态
        self.is_initialized = False
        self.current_mode = 'general'  # 'general' 或 'table'

        # 回调函数
        self.progress_callback = None
        self.page_processed_callback = None
        self.error_callback = None
    
    def initialize(self) -> bool:
        """初始化OCR引擎"""
        if not PADDLEOCR_AVAILABLE:
            if self.error_callback:
                self.error_callback("PaddleOCR未安装，请安装相关依赖")
            return False
        
        try:
            if self.progress_callback:
                self.progress_callback(10, "正在初始化OCR引擎...")

            # 获取模型路径
            ocr_model_paths = self.models_config.get_ocr_model_paths()
            table_model_paths = self.models_config.get_table_model_paths()

            # 初始化通用OCR
            ocr_kwargs = {
                'use_gpu': self.settings.use_gpu,
                'use_angle_cls': self.settings.use_angle_cls,
                'use_space_char': self.settings.use_space_char,
                'lang': self.settings.lang,
                'show_log': False
            }

            # 添加自定义模型路径
            if ocr_model_paths:
                ocr_kwargs.update(ocr_model_paths)

            if self.progress_callback:
                self.progress_callback(30, "正在加载通用OCR模型...")
            self.ocr_general = PaddleOCR(**ocr_kwargs)

            # 初始化表格识别（如果需要）
            if table_model_paths:
                if self.progress_callback:
                    self.progress_callback(60, "正在加载表格识别模型...")
                try:
                    from paddleocr import PPStructure
                    structure_kwargs = {
                        'use_gpu': self.settings.use_gpu,
                        'show_log': False
                    }
                    structure_kwargs.update(table_model_paths)
                    self.ocr_table = PPStructure(**structure_kwargs)
                except ImportError:
                    self.logger.warning("PPStructure不可用，表格模式将使用通用OCR")

            if self.progress_callback:
                self.progress_callback(100, "OCR引擎初始化完成")
            self.is_initialized = True
            return True
            
        except Exception as e:
            error_msg = f"OCR引擎初始化失败: {e}"
            self.logger.error(error_msg)
            if self.error_callback:
                self.error_callback(error_msg)
            return False
    
    def set_mode(self, mode: str):
        """设置处理模式"""
        if mode in ['general', 'table']:
            self.current_mode = mode
        else:
            raise ValueError(f"不支持的模式: {mode}")
    
    def process_image(self, image: Union[np.ndarray, Image.Image, str, Path], 
                     page_number: int = 0) -> OCRResult:
        """
        处理单张图像
        
        Args:
            image: 图像数据（numpy数组、PIL图像或文件路径）
            page_number: 页码
            
        Returns:
            OCR识别结果
        """
        if not self.is_initialized:
            raise RuntimeError("OCR引擎未初始化")
        
        result = OCRResult()
        result.page_number = page_number
        
        try:
            import time
            start_time = time.time()
            
            # 预处理图像
            processed_image = self._preprocess_image(image)
            
            if self.current_mode == 'general':
                result = self._process_general_ocr(processed_image, result)
            elif self.current_mode == 'table':
                result = self._process_table_ocr(processed_image, result)
            
            result.processing_time = time.time() - start_time
            
            # 调用页面处理完成回调
            if self.page_processed_callback:
                self.page_processed_callback(page_number, result)
            
            return result
            
        except Exception as e:
            error_msg = f"图像处理失败 (页面 {page_number}): {e}"
            self.logger.error(error_msg)
            if self.error_callback:
                self.error_callback(error_msg)
            raise
    
    def _preprocess_image(self, image: Union[np.ndarray, Image.Image, str, Path]) -> np.ndarray:
        """预处理图像"""
        if isinstance(image, (str, Path)):
            # 从文件路径加载
            image = ImageUtils.load_image(image)
        elif isinstance(image, Image.Image):
            # PIL图像转OpenCV
            image = ImageUtils.pil_to_cv2(image)
        elif isinstance(image, np.ndarray):
            # 已经是numpy数组
            pass
        else:
            raise ValueError(f"不支持的图像类型: {type(image)}")
        
        # 图像增强（可选）
        # image = ImageUtils.enhance_image_for_ocr(image)
        
        return image
    
    def _process_general_ocr(self, image: np.ndarray, result: OCRResult) -> OCRResult:
        """处理通用OCR"""
        try:
            # 执行OCR识别
            ocr_results = self.ocr_general.ocr(image, cls=self.settings.use_angle_cls)
            
            if not ocr_results or not ocr_results[0]:
                return result
            
            # 解析结果
            for line in ocr_results[0]:
                if len(line) >= 2:
                    bbox = line[0]  # 边界框坐标
                    text_info = line[1]  # (文本, 置信度)
                    
                    if isinstance(text_info, (list, tuple)) and len(text_info) >= 2:
                        text = text_info[0]
                        confidence = text_info[1]
                        
                        # 转换边界框格式
                        bbox_flat = [coord for point in bbox for coord in point]
                        
                        result.add_text_block(text, bbox_flat, confidence)
            
            return result
            
        except Exception as e:
            self.logger.error(f"通用OCR处理失败: {e}")
            raise
    
    def _process_table_ocr(self, image: np.ndarray, result: OCRResult) -> OCRResult:
        """处理表格OCR"""
        try:
            if self.ocr_table:
                # 使用PPStructure进行表格识别
                structure_results = self.ocr_table(image)
                
                for item in structure_results:
                    if item['type'] == 'table':
                        # 表格数据
                        table_data = {
                            'type': 'table',
                            'bbox': item['bbox'],
                            'html': item.get('res', {}).get('html', ''),
                            'confidence': item.get('confidence', 0.0)
                        }
                        result.add_table(table_data)
                    elif item['type'] == 'text':
                        # 文本数据
                        text = item.get('res', '')
                        bbox = item.get('bbox', [])
                        confidence = item.get('confidence', 0.0)
                        
                        result.add_text_block(text, bbox, confidence)
            else:
                # 回退到通用OCR
                result = self._process_general_ocr(image, result)
            
            return result
            
        except Exception as e:
            self.logger.error(f"表格OCR处理失败: {e}")
            # 回退到通用OCR
            return self._process_general_ocr(image, result)
    
    def process_multiple_images(self, images: List[Union[np.ndarray, Image.Image, str, Path]], 
                              progress_callback: Optional[Callable] = None) -> List[OCRResult]:
        """
        处理多张图像
        
        Args:
            images: 图像列表
            progress_callback: 进度回调函数
            
        Returns:
            OCR结果列表
        """
        results = []
        total_images = len(images)
        
        for i, image in enumerate(images):
            try:
                # 更新进度
                progress = int((i / total_images) * 100)
                status = f"正在处理第 {i+1}/{total_images} 页"
                if self.progress_callback:
                    self.progress_callback(progress, status)
                
                if progress_callback:
                    progress_callback(progress, status)
                
                # 处理图像
                result = self.process_image(image, page_number=i+1)
                results.append(result)
                
            except Exception as e:
                self.logger.error(f"处理第 {i+1} 页失败: {e}")
                # 创建空结果
                empty_result = OCRResult()
                empty_result.page_number = i+1
                results.append(empty_result)
        
        # 完成进度
        self.progress_updated.emit(100, f"处理完成，共 {total_images} 页")
        
        return results
    
    def cleanup(self):
        """清理资源"""
        self.ocr_general = None
        self.ocr_table = None
        self.ocr_layout = None
        self.is_initialized = False
