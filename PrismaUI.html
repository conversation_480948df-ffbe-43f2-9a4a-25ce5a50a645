<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Prisma-OCR & Translator</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Nunito:wght@400;500;600;700&display=swap" rel="stylesheet">
    <style>
        body {
            font-family: 'Nunito', sans-serif;
            background-color: #111827; /* 深灰蓝背景 */
        }
        .main-container {
            background: linear-gradient(135deg, rgba(31, 41, 55, 0.2), rgba(17, 24, 39, 0.2));
            backdrop-filter: blur(20px);
            -webkit-backdrop-filter: blur(20px);
            border: 1px solid rgba(255, 255, 255, 0.1);
        }
        .prism-gradient-text {
            background: linear-gradient(90deg, #818cf8, #c084fc, #f472b6);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
        }
        .prism-gradient-bg {
            background: linear-gradient(90deg, #6366f1, #a855f7, #ec4899);
        }
        .prism-border {
            border-image-slice: 1;
            border-image-source: linear-gradient(to right, #818cf8, #c084fc);
        }
        .glass-panel {
             background-color: rgba(31, 41, 55, 0.6);
             backdrop-filter: blur(10px);
             -webkit-backdrop-filter: blur(10px);
             border: 1px solid rgba(255, 255, 255, 0.1);
             border-radius: 1rem;
        }
        .radio-label:has(input:checked) {
            background-color: #4f46e5;
            border-color: #6366f1;
            color: white;
        }
        .progress-bar-inner {
            background: linear-gradient(90deg, #818cf8, #c084fc);
        }
        .resource-bar-inner {
            background: linear-gradient(90deg, #38bdf8, #818cf8);
        }
        input[type="checkbox"]:checked {
            background-color: #6366f1;
            border-color: #818cf8;
        }
        .window-controls button {
            transition: background-color 0.2s ease-in-out;
        }
    </style>
</head>
<body class="text-gray-200 flex items-center justify-center min-h-screen p-4">

    <div class="main-container w-full max-w-6xl mx-auto rounded-2xl shadow-2xl overflow-hidden flex flex-col border border-gray-700/30">
        <!-- 桌面应用标题栏 -->
        <div class="title-bar bg-gray-900/60 flex-shrink-0 flex items-center justify-between h-10 px-2 cursor-move" style="-webkit-app-region: drag;">
            <div class="flex items-center">
                <!-- Icon using relative path -->
                <img src="images/Prisma512.png" alt="App Icon" class="w-6 h-6 mr-2">
                <span class="text-sm font-semibold text-gray-300">Prisma-OCR & Translator</span>
            </div>
            <div class="window-controls flex items-center space-x-1" style="-webkit-app-region: no-drag;">
                <button id="minimize-btn" class="p-2 w-7 h-7 flex items-center justify-center rounded-md hover:bg-gray-700/50" title="最小化">
                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20 12H4"></path></svg>
                </button>
                <button id="maximize-btn" class="p-2 w-7 h-7 flex items-center justify-center rounded-md hover:bg-gray-700/50" title="最大化/还原">
                     <svg class="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="3" d="M3 7v10a2 2 0 002 2h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2z"></path></svg>
                </button>
                <button id="close-btn" class="p-2 w-7 h-7 flex items-center justify-center rounded-md hover:bg-red-500/80" title="关闭">
                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path></svg>
                </button>
            </div>
        </div>

        <header class="p-6 border-b border-gray-700/50 flex-shrink-0">
            <h1 class="text-3xl font-bold text-center prism-gradient-text">
                Prisma-OCR & Translator
            </h1>
            <p class="text-center text-gray-400 mt-1">本地PDF与图像OCR及翻译工具</p>
        </header>

        <main class="grid grid-cols-1 lg:grid-cols-2 gap-px bg-gray-700/50 flex-grow overflow-hidden">
            <!-- 配置面板 (左侧) -->
            <div class="p-8 bg-gray-900/80 overflow-y-auto">
                <h2 class="text-xl font-semibold mb-6 pl-4 prism-gradient-text">配置选项</h2>

                <!-- FR1: 文件输入 -->
                <div class="mb-6">
                    <label for="file-upload" class="block text-sm font-medium text-gray-300 mb-2">文件输入</label>
                    <div id="drop-zone" class="flex justify-center items-center w-full h-32 px-6 transition bg-gray-800/50 border-2 border-dashed border-gray-600 rounded-xl cursor-pointer hover:border-indigo-400">
                        <div class="text-center">
                            <svg class="mx-auto h-10 w-10 text-gray-500" stroke="currentColor" fill="none" viewBox="0 0 48 48" aria-hidden="true"><path d="M28 8H12a4 4 0 00-4 4v20m32-12v8m0 0v8a4 4 0 01-4 4H12a4 4 0 01-4-4v-4m32-4l-3.172-3.172a4 4 0 00-5.656 0L28 28M8 32l9.172-9.172a4 4 0 015.656 0L28 28m0 0l4 4m4-24h8m-4-4v8" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"></path></svg>
                            <p class="mt-1 text-sm text-gray-400">拖拽文件至此 或 <span class="font-semibold text-indigo-400">点击上传</span></p>
                            <p class="text-xs text-gray-500">支持 PDF, PNG, JPG, BMP, TIFF</p>
                            <input id="file-upload" name="file-upload" type="file" class="sr-only">
                        </div>
                    </div>
                    <div id="file-info" class="mt-2 text-sm text-gray-400 hidden">
                        已选择: <span id="file-name" class="font-medium text-indigo-300"></span>
                    </div>
                </div>

                <!-- FR2: 处理模式与选项 -->
                <div class="mb-6">
                    <fieldset>
                        <legend class="block text-sm font-medium text-gray-300 mb-2">处理模式 (必选)</legend>
                        <div class="grid grid-cols-2 gap-4">
                            <label for="mode-general" class="radio-label flex items-center p-4 border-2 border-gray-600 rounded-lg cursor-pointer transition hover:border-indigo-400">
                                <input id="mode-general" name="processing-mode" type="radio" value="general" class="h-4 w-4 text-indigo-600 border-gray-500 focus:ring-indigo-500 sr-only" checked>
                                <div class="ml-3 text-sm">
                                    <p class="font-medium">通用模式</p>
                                    <p class="text-gray-400 text-xs mt-1">提取所有文本和版面</p>
                                </div>
                            </label>
                             <label for="mode-table" class="radio-label flex items-center p-4 border-2 border-gray-600 rounded-lg cursor-pointer transition hover:border-indigo-400">
                                <input id="mode-table" name="processing-mode" type="radio" value="table" class="h-4 w-4 text-indigo-600 border-gray-500 focus:ring-indigo-500 sr-only">
                                <div class="ml-3 text-sm">
                                    <p class="font-medium">表格模式</p>
                                    <p class="text-gray-400 text-xs mt-1">专注于提取表格数据</p>
                                </div>
                            </label>
                        </div>
                    </fieldset>
                </div>

                <div class="mb-6 flex items-center">
                     <input id="enable-translation" name="enable-translation" type="checkbox" class="h-5 w-5 rounded border-gray-500 bg-gray-700 focus:ring-indigo-500 focus:ring-offset-gray-900 transition">
                    <label for="enable-translation" class="ml-3 block text-sm font-medium text-gray-300">启用翻译</label>
                </div>


                <!-- FR3: 输出配置 -->
                <div class="space-y-6">
                    <div>
                        <label for="output-format" class="block text-sm font-medium text-gray-300 mb-2">输出格式</label>
                        <select id="output-format" name="output-format" class="block w-full pl-3 pr-10 py-2 text-base border-gray-600 bg-gray-800/50 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm rounded-md">
                            <!-- Options will be populated by JS -->
                        </select>
                    </div>

                    <div>
                        <label for="output-path" class="block text-sm font-medium text-gray-300 mb-2">输出路径</label>
                        <div class="flex">
                            <input type="text" name="output-path" id="output-path" class="flex-1 block w-full rounded-none rounded-l-md bg-gray-800/50 border-gray-600 sm:text-sm" placeholder="与输入文件目录相同">
                            <button type="button" class="relative -ml-px inline-flex items-center space-x-2 px-4 py-2 border border-gray-600 text-sm font-medium rounded-r-md text-gray-300 bg-gray-700 hover:bg-gray-600 focus:outline-none focus:ring-1 focus:ring-indigo-500 focus:border-indigo-500">
                                <span>浏览</span>
                            </button>
                        </div>
                    </div>
                    
                    <div id="table-merge-option" class="flex items-center hidden">
                        <input id="merge-tables" name="merge-tables" type="checkbox" class="h-5 w-5 rounded border-gray-500 bg-gray-700 focus:ring-indigo-500 focus:ring-offset-gray-900 transition">
                        <label for="merge-tables" class="ml-3 block text-sm font-medium text-gray-300">合并所有表格至同一Sheet</label>
                    </div>
                </div>

                <div class="mt-10 text-center">
                    <button id="start-process-btn" type="button" class="w-full text-white font-bold py-3 px-4 rounded-lg transition duration-300 ease-in-out transform hover:scale-105 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-offset-gray-900 focus:ring-purple-300 disabled:opacity-50 disabled:scale-100 prism-gradient-bg">
                        开始处理
                    </button>
                </div>
            </div>

            <!-- 状态与监控面板 (右侧) -->
            <div class="p-8 bg-gray-900/80 flex flex-col overflow-y-auto">
                <h2 class="text-xl font-semibold mb-6 pl-4 prism-gradient-text">状态与监控</h2>

                <!-- FR4: 进度反馈 -->
                <div class="mb-8 glass-panel p-6 flex-grow">
                    <h3 class="font-semibold mb-4 prism-gradient-text">任务进度</h3>
                    <div class="space-y-4">
                        <div>
                            <div class="flex justify-between mb-1">
                                <span class="text-base font-medium text-indigo-300">总进度</span>
                                <span id="progress-percent" class="text-sm font-medium text-indigo-300">0%</span>
                            </div>
                            <div class="w-full bg-gray-700 rounded-full h-4">
                                <div id="progress-bar" class="progress-bar-inner h-4 rounded-full transition-all duration-500" style="width: 0%"></div>
                            </div>
                        </div>
                        <div id="status-text-container" class="text-center h-16 flex items-center justify-center bg-gray-800/50 rounded-lg">
                            <p id="status-text" class="text-gray-400">等待开始...</p>
                        </div>
                    </div>
                </div>

                <!-- FR5: 系统资源监控 -->
                <div class="mb-8 glass-panel p-6">
                    <h3 class="font-semibold mb-4 prism-gradient-text">系统资源</h3>
                    <div class="space-y-4">
                        <!-- CPU -->
                        <div>
                            <div class="flex justify-between text-sm">
                                <span>CPU</span>
                                <span id="cpu-usage">0%</span>
                            </div>
                            <div class="w-full bg-gray-700 rounded-full h-2.5 mt-1">
                                <div id="cpu-bar" class="resource-bar-inner h-2.5 rounded-full transition-all duration-500" style="width: 0%"></div>
                            </div>
                        </div>
                        <!-- Memory -->
                        <div>
                            <div class="flex justify-between text-sm">
                                <span>内存</span>
                                <span id="mem-usage">0%</span>
                            </div>
                            <div class="w-full bg-gray-700 rounded-full h-2.5 mt-1">
                                <div id="mem-bar" class="resource-bar-inner h-2.5 rounded-full transition-all duration-500" style="width: 0%"></div>
                            </div>
                        </div>
                        <!-- GPU -->
                        <div>
                             <div class="flex justify-between text-sm">
                                <span>GPU</span>
                                <span id="gpu-usage">0%</span>
                            </div>
                            <div class="w-full bg-gray-700 rounded-full h-2.5 mt-1">
                                <div id="gpu-bar" class="resource-bar-inner h-2.5 rounded-full transition-all duration-500" style="width: 0%"></div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </main>
    </div>

    <script>
        document.addEventListener('DOMContentLoaded', () => {
            // --- DOM Elements ---
            const modeGeneral = document.getElementById('mode-general');
            const modeTable = document.getElementById('mode-table');
            const outputFormatSelect = document.getElementById('output-format');
            const tableMergeOption = document.getElementById('table-merge-option');
            const startBtn = document.getElementById('start-process-btn');
            
            // File upload elements
            const dropZone = document.getElementById('drop-zone');
            const fileUploadInput = document.getElementById('file-upload');
            const fileInfo = document.getElementById('file-info');
            const fileNameSpan = document.getElementById('file-name');
            let selectedFile = null;

            // Status elements
            const progressBar = document.getElementById('progress-bar');
            const progressPercent = document.getElementById('progress-percent');
            const statusText = document.getElementById('status-text');

            // Resource monitor elements
            const cpuUsage = document.getElementById('cpu-usage');
            const cpuBar = document.getElementById('cpu-bar');
            const memUsage = document.getElementById('mem-usage');
            const memBar = document.getElementById('mem-bar');
            const gpuUsage = document.getElementById('gpu-usage');
            const gpuBar = document.getElementById('gpu-bar');

            // --- State & Config ---
            const outputFormats = {
                general: [
                    { value: 'txt', text: '纯文本 (.txt)' },
                    { value: 'docx', text: 'Word 文档 (.docx)' },
                    { value: 'pdf', text: '可搜索 PDF (.pdf)' },
                    { value: 'xlsx', text: 'Excel 表格 (.xlsx)' },
                ],
                table: [
                    { value: 'xlsx', text: 'Excel 表格 (.xlsx)' }
                ]
            };
            
            // --- Functions ---
            function updateOutputFormats() {
                const currentMode = document.querySelector('input[name="processing-mode"]:checked').value;
                const formats = outputFormats[currentMode];
                
                outputFormatSelect.innerHTML = ''; // Clear existing options
                
                formats.forEach(format => {
                    const option = document.createElement('option');
                    option.value = format.value;
                    option.textContent = format.text;
                    outputFormatSelect.appendChild(option);
                });
            }

            function toggleTableMergeOption() {
                const currentMode = document.querySelector('input[name="processing-mode"]:checked').value;
                if (currentMode === 'table') {
                    tableMergeOption.classList.remove('hidden');
                } else {
                    tableMergeOption.classList.add('hidden');
                }
            }

            async function handleFileSelect(files) {
                if (files.length > 0) {
                    const file = files[0];

                    try {
                        // 调用Python API验证和选择文件
                        const result = await pywebview.api.select_file(file.path || file.name);

                        if (result.success) {
                            selectedFile = file;
                            fileNameSpan.textContent = result.file_info.name;
                            fileInfo.classList.remove('hidden');

                            // 显示文件信息
                            console.log('文件信息:', result.file_info);
                        } else {
                            alert(`文件选择失败: ${result.error}`);
                        }
                    } catch (error) {
                        console.error('文件选择失败:', error);
                        // 回退到客户端处理
                        selectedFile = file;
                        fileNameSpan.textContent = file.name;
                        fileInfo.classList.remove('hidden');
                    }
                }
            }

            function getProcessingConfig() {
                const mode = document.querySelector('input[name="processing-mode"]:checked').value;
                const enableTranslation = document.getElementById('enable-translation').checked;
                const outputFormat = document.getElementById('output-format').value;
                const outputPath = document.getElementById('output-path').value;
                const mergeTables = document.getElementById('merge-tables').checked;

                return {
                    mode: mode,
                    translation: {
                        enabled: enableTranslation
                    },
                    output: {
                        format: outputFormat.toLowerCase(),
                        directory: outputPath,
                        merge_tables: mergeTables
                    }
                };
            }

            async function monitorProcessingProgress() {
                const checkProgress = async () => {
                    try {
                        const status = await pywebview.api.get_processing_status();

                        // 更新进度条
                        progressBar.style.width = `${status.progress}%`;
                        progressPercent.textContent = `${status.progress}%`;
                        statusText.textContent = status.status_text;

                        if (status.error) {
                            statusText.textContent = `错误: ${status.error}`;
                            statusText.classList.add('text-red-400');
                            startBtn.disabled = false;
                            return;
                        }

                        if (status.is_processing) {
                            // 继续监控
                            setTimeout(checkProgress, 500);
                        } else {
                            // 处理完成
                            startBtn.disabled = false;
                            if (status.progress === 100) {
                                statusText.classList.remove('text-red-400');
                            }
                        }
                    } catch (error) {
                        console.error('获取处理状态失败:', error);
                        startBtn.disabled = false;
                    }
                };

                checkProgress();
            }

            // --- Event Listeners ---
            modeGeneral.addEventListener('change', () => {
                updateOutputFormats();
                toggleTableMergeOption();
            });
            modeTable.addEventListener('change', () => {
                updateOutputFormats();
                toggleTableMergeOption();
            });
            
            // File Upload Listeners
            dropZone.addEventListener('click', () => fileUploadInput.click());
            fileUploadInput.addEventListener('change', (e) => handleFileSelect(e.target.files));
            
            ['dragenter', 'dragover', 'dragleave', 'drop'].forEach(eventName => {
                dropZone.addEventListener(eventName, (e) => {
                    e.preventDefault();
                    e.stopPropagation();
                }, false);
            });

            ['dragenter', 'dragover'].forEach(eventName => {
                dropZone.addEventListener(eventName, () => {
                    dropZone.classList.add('border-indigo-400');
                }, false);
            });

            ['dragleave', 'drop'].forEach(eventName => {
                dropZone.addEventListener(eventName, () => {
                    dropZone.classList.remove('border-indigo-400');
                }, false);
            });

            dropZone.addEventListener('drop', (e) => {
                handleFileSelect(e.dataTransfer.files);
            }, false);

            startBtn.addEventListener('click', async () => {
                if (!selectedFile) {
                    statusText.textContent = '错误: 请先选择一个文件!';
                    statusText.classList.add('text-red-400');
                    return;
                }

                startBtn.disabled = true;
                statusText.classList.remove('text-red-400');

                try {
                    // 获取处理配置
                    const config = getProcessingConfig();

                    // 调用Python API开始处理
                    const result = await pywebview.api.start_processing(config);

                    if (result.success) {
                        // 开始监控处理进度
                        monitorProcessingProgress();
                    } else {
                        statusText.textContent = `错误: ${result.error}`;
                        statusText.classList.add('text-red-400');
                        startBtn.disabled = false;
                    }
                } catch (error) {
                    console.error('开始处理失败:', error);
                    statusText.textContent = '错误: 启动处理失败';
                    statusText.classList.add('text-red-400');
                    startBtn.disabled = false;
                }
            });
            
            // --- 系统资源监控 ---
            async function updateSystemResources() {
                try {
                    const resources = await pywebview.api.get_system_resources();

                    const cpuPercent = Math.round(resources.cpu_percent || 0);
                    const memPercent = Math.round(resources.memory_percent || 0);
                    const gpuPercent = Math.round(resources.gpu_percent || 0);

                    cpuUsage.textContent = `${cpuPercent}%`;
                    cpuBar.style.width = `${cpuPercent}%`;
                    memUsage.textContent = `${memPercent}%`;
                    memBar.style.width = `${memPercent}%`;
                    gpuUsage.textContent = `${gpuPercent}%`;
                    gpuBar.style.width = `${gpuPercent}%`;
                } catch (error) {
                    console.error('获取系统资源失败:', error);
                }
            }

            // 定期更新系统资源
            setInterval(updateSystemResources, 2000);

            // 初始更新
            updateSystemResources();

            // --- 窗口控制按钮事件 ---
            const minimizeBtn = document.getElementById('minimize-btn');
            const maximizeBtn = document.getElementById('maximize-btn');
            const closeBtn = document.getElementById('close-btn');

            if (minimizeBtn) {
                minimizeBtn.addEventListener('click', async () => {
                    try {
                        await pywebview.api.minimize_window();
                    } catch (error) {
                        console.error('最小化窗口失败:', error);
                    }
                });
            }

            if (maximizeBtn) {
                maximizeBtn.addEventListener('click', async () => {
                    try {
                        await pywebview.api.maximize_window();
                    } catch (error) {
                        console.error('切换窗口状态失败:', error);
                    }
                });
            }

            if (closeBtn) {
                closeBtn.addEventListener('click', async () => {
                    try {
                        await pywebview.api.close_window();
                    } catch (error) {
                        console.error('关闭窗口失败:', error);
                    }
                });
            }

            // --- Initial Setup ---
            updateOutputFormats();
            toggleTableMergeOption();
        });
    </script>
</body>
</html>

